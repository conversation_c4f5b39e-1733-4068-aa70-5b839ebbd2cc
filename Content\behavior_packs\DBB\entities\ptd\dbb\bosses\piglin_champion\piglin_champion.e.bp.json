{
  "format_version": "1.21.70",
  "minecraft:entity": {
    "description": {
      "identifier": "ptd_dbb:piglin_champion",
      "is_spawnable": true,
      "is_summonable": true,
      "properties": {
        "ptd_dbb:spawning": {
          "type": "bool",
          "client_sync": true,
          "default": true
        },
        "ptd_dbb:dead": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },

        "ptd_dbb:attack": {
          "type": "enum",
          "client_sync": true,
          "default": "none",
          "values": [
            "none",
            "horizontal",
            "vertical",
            "foot_stomp",
            "spin_slam",
            "body_slam",
            "upchuck",
            "charging",
            "healing",
            "summoning_chant",
            "stunned_standing",
            "stunned_sitting"
          ]
        },
        "ptd_dbb:cooling_down": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },
        "ptd_dbb:attack_started": {
          "type": "bool",
          "client_sync": false,
          "default": false
        },
        "ptd_dbb:last_heal_threshold": {
          "type": "int",
          "client_sync": false,
          "range": [0, 3],
          "default": 3
        },
        "ptd_dbb:stun_standing_triggered": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },
        "ptd_dbb:stun_sitting_triggered": {
          "type": "bool",
          "client_sync": true,
          "default": false
        }
      },
      "animations": {
        "walk": "animation.ptd_dbb_piglin_champion.walk",
        "spawn": "animation.ptd_dbb_piglin_champion.spawn",
        "general": "controller.animation.ptd_dbb_piglin_champion.general"
      },
      "scripts": {
        "animate": [
          "general",
          {
            "spawn": "q.property('ptd_dbb:spawning') == true"
          }
        ]
      }
    },
    "component_groups": {
      "ptd_dbb:spawning": {
        "minecraft:is_collidable": {},
        "minecraft:timer": {
          "time": 5.6667,
          "looping": false,
          "time_down_event": {
            "event": "ptd_dbb:on_spawn",
            "target": "self"
          }
        },
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "cause": "all",
              "deals_damage": "no"
            }
          ]
        }
      },
      "ptd_dbb:default": {
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "on_damage": {
                "filters": {
                  "test": "actor_health",
                  "subject": "self",
                  "operator": "<=",
                  "value": 10
                },
                "event": "ptd_dbb:dead",
                "target": "self"
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "domain": "ptd_dbb:dead",
                  "subject": "self",
                  "value": true
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "enum_property",
                  "domain": "ptd_dbb:attack",
                  "subject": "self",
                  "value": "healing"
                }
              },
              "deals_damage": "no"
            }
          ]
        },
        "minecraft:movement.basic": {},
        "minecraft:behavior.float": {
          "priority": 0
        },
        "minecraft:navigation.walk": {
          "avoid_damage_blocks": true,
          "can_pass_doors": true,
          "can_jump": true,
          "is_amphibious": true,
          "can_float": true
        }
      },
      "ptd_dbb:targeting": {
        "minecraft:environment_sensor": {
          "triggers": [
            {
              "filters": [
                {
                  "test": "target_distance",
                  "subject": "other",
                  "operator": "<=",
                  "value": 32
                },
                {
                  "test": "enum_property",
                  "subject": "self",
                  "domain": "ptd_dbb:attack",
                  "operator": "==",
                  "value": "none"
                },
                {
                  "test": "bool_property",
                  "subject": "self",
                  "domain": "ptd_dbb:cooling_down",
                  "operator": "==",
                  "value": false
                }
              ],
              "event": "ptd_dbb:attack"
            }
          ]
        },
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 0,
          "must_see": true,
          "attack_interval": 1,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "reevaluate_description": true,
          "entity_types": [
            {
              "priority": 0,
              "max_dist": 64,
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "player"
              }
            },
            {
              "priority": 1,
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "boss"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "piglin_champion"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            },
            {
              "priority": 2,
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "minion"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "piglin_champion"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            }
          ]
        }
      },
      "ptd_dbb:melee": {
        "minecraft:attack": {
          "damage": 0
        },
        "minecraft:movement": {
          "max": 0.15
        },
        "minecraft:behavior.random_look_around": {
          "priority": 5
        },
        "minecraft:behavior.random_stroll": {
          "priority": 4
        }
      },
      "ptd_dbb:charging2": {
        "minecraft:movement": {
          "max": 0.5
        }
      },
      "ptd_dbb:collidable": {
        "minecraft:is_collidable": {}
      },
      "ptd_dbb:dead": {
        "minecraft:timer": {
          "time": 20,
          "looping": false,
          "time_down_event": {
            "event": "ptd_dbb:despawn",
            "target": "self"
          }
        },
        "minecraft:collision_box": {
          "width": 2.5,
          "height": 2.6
        },
        "minecraft:custom_hit_test": {
          "hitboxes": [
            {
              "width": 1.9,
              "height": 2.4,
              "pivot": [0, 1.2, -1.7]
            }
          ]
        },
        "minecraft:is_collidable": {},
        "minecraft:movement": {
          "max": 0
        },
        "minecraft:navigation.walk": {
          "is_amphibious": false,
          "can_pass_doors": false,
          "can_walk": false,
          "can_swim": false,
          "can_sink": false,
          "avoid_sun": false
        },
        "minecraft:behavior.random_look_around": {
          "priority": 999999
        },
        "minecraft:behavior.random_stroll": {
          "priority": 999999
        },
        "minecraft:body_rotation_blocked": {}
      },
      "ptd_dbb:despawn": {
        "minecraft:instant_despawn": {}
      }
    },
    "events": {
      "minecraft:entity_spawned": {
        "add": {
          "component_groups": ["ptd_dbb:spawning"]
        }
      },
      "ptd_dbb:on_spawn": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_dbb:spawning"]
            },
            "set_property": {
              "ptd_dbb:spawning": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default", "ptd_dbb:targeting", "ptd_dbb:melee"]
            }
          }
        ]
      },
      "ptd_dbb:despawn": {
        "add": {
          "component_groups": ["ptd_dbb:despawn"]
        }
      },
      "ptd_dbb:attack": {},
      "ptd_dbb:healing_ability": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "healing"
            },
            "add": {
              "component_groups": [
                "ptd_dbb:collidable",
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:end_healing": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_dbb:collidable"]
            },
            "add": {
              "component_groups": [
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:dead": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:dead": true
            }
          },
          {
            "remove": {
              "component_groups": ["ptd_dbb:targeting", "ptd_dbb:melee"]
            }
          },
          {
            "add": {
              "component_groups": [
                "ptd_dbb:dead",
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:horizontal_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "horizontal"
            }
          }
        ]
      },
      "ptd_dbb:vertical_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "vertical"
            }
          }
        ]
      },
      "ptd_dbb:foot_stomp_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "foot_stomp"
            }
          }
        ]
      },
      "ptd_dbb:spin_slam_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "spin_slam"
            }
          }
        ]
      },
      "ptd_dbb:body_slam_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "body_slam"
            }
          }
        ]
      },
      "ptd_dbb:upchuck_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "upchuck"
            }
          }
        ]
      },
      "ptd_dbb:charging_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "charging"
            }
          }
        ]
      },
      "ptd_dbb:summoning_chant_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "summoning_chant"
            }
          }
        ]
      },
      "ptd_dbb:charging2": {
        "sequence": [
          {
            "add": {
              "component_groups": [
                "ptd_dbb:charging2",
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:on_charging2": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_dbb:charging2"]
            }
          },
          {
            "add": {
              "component_groups": [
                "ptd_dbb:melee",
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:stun_after_charge": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_dbb:melee"]
            }
          },
          {
            "add": {
              "component_groups": [
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:recover_after_charge": {
        "sequence": [
          {
            "add": {
              "component_groups": [
                "ptd_dbb:melee",
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:reset_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "none",
              "ptd_dbb:cooling_down": true,
              "ptd_dbb:attack_started": false
            }
          }
        ]
      },
      "ptd_dbb:stunned_standing": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "stunned_standing",
              "ptd_dbb:stun_standing_triggered": true
            },
            "add": {
              "component_groups": ["ptd_dbb:collidable", "ptd_dbb:default"]
            }
          }
        ]
      },
      "ptd_dbb:stunned_sitting": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "stunned_sitting",
              "ptd_dbb:stun_sitting_triggered": true
            },
            "add": {
              "component_groups": ["ptd_dbb:collidable", "ptd_dbb:default"]
            }
          }
        ]
      },
      "ptd_dbb:remove_targeting": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_dbb:targeting", "ptd_dbb:default"]
            }
          }
        ]
      },
      "ptd_dbb:restore_targeting": {
        "sequence": [
          {
            "add": {
              "component_groups": ["ptd_dbb:targeting", "ptd_dbb:default"]
            }
          }
        ]
      },
      "ptd_dbb:on_load": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "none",
              "ptd_dbb:cooling_down": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default", "ptd_dbb:targeting"]
            }
          }
        ]
      }
    },
    "components": {
      "minecraft:type_family": {
        "family": ["piglin_champion", "boss"]
      },
      "minecraft:collision_box": {
        "width": 2,
        "height": 3.5
      },
      "minecraft:custom_hit_test": {
        "hitboxes": [
          {
            "width": 2.3,
            "height": 4.5,
            "pivot": [0, 2.25, 0]
          }
        ]
      },
      "minecraft:health": {
        "value": 1800,
        "max": 1800
      },
      "minecraft:boss": {
        "hud_range": 64,
        "name": "Piglin Champion",
        "should_darken_sky": false
      },
      "minecraft:damage_sensor": {
        "triggers": [
          {
            "on_damage": {
              "filters": {
                "test": "actor_health",
                "subject": "self",
                "operator": "<=",
                "value": 10
              },
              "event": "ptd_dbb:dead",
              "target": "self"
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "bool_property",
                "domain": "ptd_dbb:dead",
                "subject": "self",
                "value": true
              }
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "enum_property",
                "domain": "ptd_dbb:attack",
                "subject": "self",
                "value": "healing"
              }
            },
            "deals_damage": "no"
          }
        ]
      },
      "minecraft:variable_max_auto_step": {
        "base_value": 1.0625,
        "jump_prevented_value": 1.0625
      },
      "minecraft:jump.static": {},
      "minecraft:movement.basic": {},
      "minecraft:navigation.walk": {
        "can_path_over_water": true,
        "can_pass_doors": true,
        "can_break_doors": false,
        "avoid_water": true
      },
      /*
       * This allows the entity to follow/go to the target without attacking
       * Since the attacks are handled in the scripts
       */
      "minecraft:behavior.melee_box_attack": {
        "priority": 2,
        "can_spread_on_fire": true,
        "speed_multiplier": 1,
        "horizontal_reach": 0,
        "cooldown_time": 999999999
      },
      "minecraft:behavior.float": {
        "priority": 0
      },
      "minecraft:knockback_resistance": {
        "value": 0.9
      },
      "minecraft:follow_range": {
        "value": 256,
        "max": 256
      },
      "minecraft:physics": {},
      "minecraft:is_stackable": {},
      "minecraft:floats_in_liquid": {},
      "minecraft:persistent": {},
      "minecraft:conditional_bandwidth_optimization": {}
    }
  }
}
