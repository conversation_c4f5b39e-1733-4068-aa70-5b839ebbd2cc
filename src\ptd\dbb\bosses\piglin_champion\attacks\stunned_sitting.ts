import { Entity, system } from "@minecraft/server";
import { stopPiglinChampionSounds } from "../soundManager";

/**
 * Total animation time in ticks for stunned sitting
 */
const ANIMATION_TIME = 333;

/**
 * Cooldown time in ticks after the stun completes
 */
const COOLDOWN_TIME = 20;

/**
 * Executes the stunned sitting for the Piglin Champion using the new timing system
 * Uses localized runTimeout for stun duration, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeStunnedSitting(piglinChampion: Entity): void {
  // Apply slowness effect for the entire duration and remove component groups
  piglinChampion.addEffect("minecraft:slowness", ANIMATION_TIME, { amplifier: 250, showParticles: false });

  // Remove default and targeting component groups to prevent looking around
  piglinChampion.triggerEvent("ptd_dbb:remove_targeting");

  // Restore targeting after the stun duration (but don't reset attack - let animation controller handle it)
  let restoreTargeting = system.runTimeout(() => {
    try {
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(restoreTargeting);
        return;
      }

      // Only restore component groups and stop sounds - let animation controller reset attack naturally
      piglinChampion.triggerEvent("ptd_dbb:restore_targeting");
      stopPiglinChampionSounds(piglinChampion);
      system.clearRun(restoreTargeting);
    } catch (error) {
      system.clearRun(restoreTargeting);
    }
  }, ANIMATION_TIME);

  // Wait for cooldown, then set cooldown property to false to allow next attack
  let cooldown = system.runTimeout(() => {
    try {
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldown);
        return;
      }
      piglinChampion.setProperty("ptd_dbb:cooling_down", false);
      system.clearRun(cooldown);
    } catch (error) {
      system.clearRun(cooldown);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);

  return;
}
